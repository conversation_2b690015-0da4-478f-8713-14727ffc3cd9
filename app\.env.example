# FODMAP Recipe API Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Node.js environment (development, production, test)
NODE_ENV=development

# Application name for monitoring and logging
APP_NAME=fodmap-recipe-api

# Server port
PORT=3000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL connection string
# Format: postgresql://username:password@host:port/database
DATABASE_URL=postgresql://username:password@localhost:5432/fodmap_recipes

# Database connection pool settings
DB_POOL_MAX=20
DB_POOL_MIN=2
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=10000
DB_MAX_USES=7500
DB_QUERY_TIMEOUT=30000
DB_STATEMENT_TIMEOUT=60000

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# CORS allowed origins (comma-separated)
# Use * for development, specific domains for production
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================

# General API rate limiting (requests per window)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Strict rate limiting for write operations
STRICT_RATE_LIMIT_WINDOW_MS=900000
STRICT_RATE_LIMIT_MAX_REQUESTS=20

# Bulk operations rate limiting
BULK_RATE_LIMIT_WINDOW_MS=3600000
BULK_RATE_LIMIT_MAX_REQUESTS=5

# Search operations rate limiting
SEARCH_RATE_LIMIT_WINDOW_MS=300000
SEARCH_RATE_LIMIT_MAX_REQUESTS=50

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Enable request logging
ENABLE_REQUEST_LOGGING=true

# Log file path (optional, logs to console if not set)
# LOG_FILE_PATH=/var/log/fodmap-api.log

# =============================================================================
# MONITORING AND HEALTH CHECKS
# =============================================================================

# Health check endpoint configuration
HEALTH_CHECK_ENABLED=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable development features (detailed error messages, etc.)
DEVELOPMENT_MODE=true

# Enable SQL query logging in development
LOG_SQL_QUERIES=true

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

# SSL/TLS settings for production
# SSL_CERT_PATH=/path/to/certificate.crt
# SSL_KEY_PATH=/path/to/private.key

# Session secret for production (generate a strong random string)
# SESSION_SECRET=your-super-secret-session-key-here

# =============================================================================
# EXTERNAL SERVICES (if needed in future)
# =============================================================================

# Email service configuration (for notifications, etc.)
# SMTP_HOST=smtp.example.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-email-password

# File upload service (for recipe images)
# UPLOAD_SERVICE=local
# UPLOAD_PATH=/uploads
# MAX_FILE_SIZE=5242880

# =============================================================================
# CACHE CONFIGURATION (if Redis is added later)
# =============================================================================

# Redis connection for caching
# REDIS_URL=redis://localhost:6379
# CACHE_TTL=3600

# =============================================================================
# API DOCUMENTATION
# =============================================================================

# Swagger/OpenAPI documentation
# API_DOCS_ENABLED=true
# API_DOCS_PATH=/api-docs

# =============================================================================
# NOTES
# =============================================================================

# 1. Never commit the actual .env file to version control
# 2. Use strong, unique passwords and secrets in production
# 3. Regularly rotate secrets and API keys
# 4. Use environment-specific values for each deployment
# 5. Consider using a secrets management service for production
