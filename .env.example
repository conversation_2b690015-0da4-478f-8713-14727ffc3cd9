# Database Configuration
POSTGRES_DB=fodmap_db
POSTGRES_USER=app_user
POSTGRES_PASSWORD=your_strong_password_here
APP_DB_USER=recipes_user
APP_DB_PASSWORD=your_app_password_here

# PgAdmin Configuration (for development)
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin_password

# Application Configuration
DATABASE_URL=postgresql://recipes_user:your_app_password_here@localhost:5432/fodmap_db
NODE_ENV=development
PORT=3000

# Security (for production)
# JWT_SECRET=your_jwt_secret_here
# RATE_LIMIT_WINDOW_MS=900000
# RATE_LIMIT_MAX_REQUESTS=100

# Logging
# LOG_LEVEL=info
# LOG_FILE=logs/app.log

# CORS Configuration (for production)
# CORS_ORIGIN=https://your-frontend-domain.com
# CORS_CREDENTIALS=true
