{"name": "app", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "joi": "^17.13.3", "morgan": "^1.10.0", "pg": "^8.16.0"}, "devDependencies": {"nodemon": "^3.1.10"}}